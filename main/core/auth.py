from fastapi import HTTPException, Header
import os
import requests

async def verify_token(authorization: str = Header(None)):
    if not authorization:
        raise HTTPException(status_code=401, detail="No token provided")

    try:
        response = requests.post(
            f"http://{os.getenv('DJANGO_CONTAINER_NAME')}:8001/auth/verify/",
            headers={"Authorization": authorization}
        )
        if response.status_code != 200:
            raise HTTPException(status_code=401, detail="Invalid token")
    except requests.RequestException:
        raise HTTPException(status_code=503, detail="Authentication service unavailable")
